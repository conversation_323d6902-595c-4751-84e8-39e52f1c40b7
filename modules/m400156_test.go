package modules

import (
	"fmt"
	"math/rand"
	"os"
	"testing"
)

func TestM400156(t *testing.T) {
	config, _ := os.ReadFile("../bin/configs/400156.yaml")
	m := m400156{}
	m.Init(config)

	var grid []int16
	var dop map[string]any
	// for i := range [10000]int{} {
	rd := rand.New(rand.NewSource(int64(4868)))
	grid = m.generateGrid(rd)
	dop = m.genereateDop(rd)

	scatterInCols := map[int]int{0: 0, 1: 0, 2: 0, 3: 0, 4: 0}
	iconPos := map[int16][]int16{}
	for row := 0; row < m.Config.Row; row++ {
		for col := 0; col < m.Config.Column; col++ {
			index := row*m.Config.Column + col
			if grid[index] == m.Config.ScatterIcon {
				scatterInCols[col]++
			}

			iconPos[grid[index]] = append(iconPos[grid[index]], int16(index))
		}
	}

	var replaceIcon int16
	box := make([]int16, len(grid))
	copy(box, grid)
	for k, v := range scatterInCols {
		if dop["upBox"].([][]int16)[k][0] > 24 && dop["upBox"].([][]int16)[k][0] < 29 && v >= 1 {
			// fmt.Println(i)
			fmt.Println(dop["upBox"].([][]int16)[k][0])
			switch dop["upBox"].([][]int16)[k][0] {
			case 25:
				replaceIcon = 0
			case 26:
				replaceIcon = 2
			case 27:
				replaceIcon = 3
			case 28:
				replaceIcon = 4
			}

			for i, ico := range grid {
				row := i / 5
				col := i % 5
				index := row*5 + col
				if ico > 8 {
					fmt.Println(index, ico)
					box[index] = replaceIcon
				}
			}
		}
	}
	// }
	fmt.Printf("%+v\n", dop)
	fmt.Println("================")

	for i := 0; i < len(grid); i += 5 {
		fmt.Println(grid[i : i+5])
	}
	fmt.Println("================")
	for i := 0; i < len(grid); i += 5 {
		fmt.Println(box[i : i+5])
	}
}
